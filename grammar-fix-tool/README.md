# Grammar Fix Tool

A modern grammar correction tool powered by ChatGPT API. This application provides a clean, side-by-side interface for fixing grammar, spelling, and improving text clarity.

## Features

- **Side-by-side interface**: Original text on the left, corrected text on the right
- **ChatGPT integration**: Uses OpenAI's GPT-3.5-turbo model for accurate grammar correction
- **Real-time character count**: Track text length in both input and output areas
- **Copy functionality**: Easy one-click copying of corrected text
- **Error handling**: Comprehensive error messages for API issues
- **Responsive design**: Works on desktop and mobile devices
- **Modern UI**: Built with Tailwind CSS for a clean, professional look

## Setup Instructions

1. **Clone and install dependencies**:
   ```bash
   cd grammar-fix-tool
   npm install
   ```

2. **Set up your OpenAI API key**:
   - Copy `.env.example` to `.env`
   - Replace `your_openai_api_key_here` with your actual OpenAI API key
   - Get your API key from [OpenAI Platform](https://platform.openai.com/api-keys)

3. **Run the development server**:
   ```bash
   npm run dev
   ```

4. **Open your browser** and navigate to `http://localhost:5173`

## Usage

1. Enter or paste your text in the left text area
2. Click "Fix Grammar" to process your text with ChatGPT
3. Review the corrected text in the right text area
4. Use the "Copy" button to copy the fixed text to your clipboard
5. Use "Clear" to reset both text areas

## Important Notes

- **API Key Security**: This demo uses the API key in the browser for simplicity. In production, implement a backend proxy to keep your API key secure.
- **Rate Limits**: Be aware of OpenAI's rate limits and usage costs.
- **Text Length**: The tool supports up to 2000 tokens per request.

## Technologies Used

- **React 18**: Modern React with hooks
- **Vite**: Fast build tool and development server
- **Tailwind CSS**: Utility-first CSS framework
- **OpenAI API**: GPT-3.5-turbo for grammar correction

## Build for Production

```bash
npm run build
```

The built files will be in the `dist` directory.
