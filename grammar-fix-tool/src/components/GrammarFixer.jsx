import { useState } from 'react';
import grammarService from '../services/grammarService';

const GrammarFixer = () => {
  const [inputText, setInputText] = useState('');
  const [outputText, setOutputText] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');

  const handleFixGrammar = async () => {
    if (!inputText.trim()) {
      setError('Please enter some text to fix');
      return;
    }

    setIsLoading(true);
    setError('');
    
    try {
      const fixedText = await grammarService.fixGrammar(inputText);
      setOutputText(fixedText);
    } catch (err) {
      setError(err.message);
      setOutputText('');
    } finally {
      setIsLoading(false);
    }
  };

  const handleClear = () => {
    setInputText('');
    setOutputText('');
    setError('');
  };

  const handleCopyOutput = async () => {
    if (outputText) {
      try {
        await navigator.clipboard.writeText(outputText);
        // You could add a toast notification here
      } catch (err) {
        console.error('Failed to copy text:', err);
      }
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-7xl mx-auto">
        <div className="text-center mb-8">
          <h1 className="text-4xl font-bold text-gray-800 mb-2">
            Grammar Fix Tool
          </h1>
          <p className="text-gray-600 text-lg">
            Powered by ChatGPT - Fix grammar, spelling, and improve your text
          </p>
        </div>

        {error && (
          <div className="mb-6 p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
            <p className="font-medium">Error:</p>
            <p>{error}</p>
          </div>
        )}

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Input Section */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Original Text
              </h2>
              <span className="text-sm text-gray-500">
                {inputText.length} characters
              </span>
            </div>
            <textarea
              value={inputText}
              onChange={(e) => setInputText(e.target.value)}
              placeholder="Enter your text here to fix grammar and spelling..."
              className="w-full h-80 p-4 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent outline-none"
              disabled={isLoading}
            />
          </div>

          {/* Output Section */}
          <div className="bg-white rounded-lg shadow-lg p-6">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-xl font-semibold text-gray-800">
                Fixed Text
              </h2>
              <div className="flex items-center gap-2">
                <span className="text-sm text-gray-500">
                  {outputText.length} characters
                </span>
                {outputText && (
                  <button
                    onClick={handleCopyOutput}
                    className="text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-md transition-colors"
                  >
                    Copy
                  </button>
                )}
              </div>
            </div>
            <textarea
              value={outputText}
              readOnly
              placeholder="Fixed text will appear here..."
              className="w-full h-80 p-4 border border-gray-300 rounded-lg resize-none bg-gray-50 focus:ring-2 focus:ring-green-500 focus:border-transparent outline-none"
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-center gap-4">
          <button
            onClick={handleFixGrammar}
            disabled={isLoading || !inputText.trim()}
            className="bg-blue-600 hover:bg-blue-700 disabled:bg-gray-400 text-white font-semibold py-3 px-8 rounded-lg transition-colors flex items-center gap-2"
          >
            {isLoading ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-2 border-white border-t-transparent"></div>
                Fixing Grammar...
              </>
            ) : (
              'Fix Grammar'
            )}
          </button>
          
          <button
            onClick={handleClear}
            disabled={isLoading}
            className="bg-gray-500 hover:bg-gray-600 disabled:bg-gray-400 text-white font-semibold py-3 px-8 rounded-lg transition-colors"
          >
            Clear
          </button>
        </div>

        {/* Instructions */}
        <div className="mt-8 bg-white rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-800 mb-3">
            How to use:
          </h3>
          <ol className="list-decimal list-inside space-y-2 text-gray-600">
            <li>Enter or paste your text in the left text area</li>
            <li>Click "Fix Grammar" to process your text</li>
            <li>Review the corrected text in the right text area</li>
            <li>Copy the fixed text using the "Copy" button</li>
          </ol>
          <div className="mt-4 p-3 bg-yellow-50 border border-yellow-200 rounded-md">
            <p className="text-sm text-yellow-800">
              <strong>Note:</strong> Make sure to set your OpenAI API key in the .env file before using this tool.
            </p>
          </div>
        </div>
      </div>
    </div>
  );
};

export default GrammarFixer;
